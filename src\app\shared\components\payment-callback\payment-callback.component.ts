import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { LoggerService } from '../../interceptors/logger.service';

@Component({
  selector: 'app-payment-callback',
  templateUrl: './payment-callback.component.html',
  styleUrls: ['./payment-callback.component.css']
})
export class PaymentCallbackComponent implements OnInit {
  private logger: LoggerService;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private toastrService: ToastrService
  ) {
    this.logger = LoggerService.createLogger('PaymentCallbackComponent');
  }

  ngOnInit(): void {
    this.logger.debug('Payment callback component initialized');
    
    // Get query parameters from the callback URL
    this.route.queryParams.subscribe(params => {
      this.logger.debug('Payment callback params:', params);
      
      // Handle the callback parameters
      if (params['status']) {
        this.handlePaymentCallback(params);
      } else {
        // If no parameters, redirect to home
        this.logger.error('No payment callback parameters found');
        this.router.navigate(['/home']);
      }
    });
  }

  private handlePaymentCallback(params: any): void {
    const status = params['status'];
    const transactionId = params['transactionId'];
    const bookingId = params['bookingId'];
    
    this.logger.debug('Handling payment callback with status:', status);
    
    switch (status?.toLowerCase()) {
      case 'success':
      case 'completed':
        this.handleSuccessCallback(bookingId, transactionId);
        break;
        
      case 'failed':
      case 'failure':
        this.handleFailureCallback(transactionId);
        break;
        
      case 'cancelled':
      case 'cancel':
        this.handleCancelCallback(transactionId);
        break;
        
      default:
        this.logger.error('Unknown payment status:', status);
        this.toastrService.warning('Unknown payment status received');
        this.router.navigate(['/home']);
        break;
    }
  }

  private handleSuccessCallback(bookingId: string, transactionId: string): void {
    this.logger.debug('Payment successful:', { bookingId, transactionId });
    this.toastrService.success('Payment completed successfully!');
    
    if (bookingId) {
      // Navigate to booking receipt page
      this.router.navigate(['/userprofile/booking-receipt', bookingId]);
    } else {
      // Navigate to user bookings page
      this.router.navigate(['/userprofile/overview']);
    }
  }

  private handleFailureCallback(transactionId: string): void {
    this.logger.debug('Payment failed:', { transactionId });
    this.toastrService.error('Payment failed. Please try again.');
    this.router.navigate(['/home']);
  }

  private handleCancelCallback(transactionId: string): void {
    this.logger.debug('Payment cancelled:', { transactionId });
    this.toastrService.info('Payment was cancelled by user');
    this.router.navigate(['/home']);
  }
}
