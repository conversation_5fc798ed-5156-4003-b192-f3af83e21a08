import { PaymentResponseService } from './../../services/payment-response.service';
import { BookingRequest } from './../../models/booking.model';
import { BookingReceiptData, BookingReceiptResponse } from './../../models/booking-receipt.model';
import { BookingAPIService } from './../../services/booking-api.service';
import { AppConfig } from 'src/app/configs/app.config';
import { Router, ActivatedRoute } from '@angular/router';
import { LoggerService } from './../../interceptors/logger.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-confirmation-page',
  templateUrl: './confirmation-page.component.html',
  styleUrls: ['./confirmation-page.component.css']
})
export class ConfirmationPageComponent implements OnInit, OnDestroy {
  private logger: LoggerService;
  private routeSubscription: Subscription;
  private bookingSubscription: Subscription;

  bookingId: string;
  bookingRequest: BookingRequest;
  bookingData: BookingReceiptData;
  paymentData: any;
  imageFolderPath: string = AppConfig.imageFolderPath;
  isLoading: boolean = true;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private paymentResponse: PaymentResponseService,
    private paymentRepsponseService: PaymentResponseService,
    private bookingAPIService: BookingAPIService) {
    this.logger = LoggerService.createLogger('ConfirmationPageComponent');
  }

  ngOnInit(): void {
    this.logger.debug('ConfirmationPageComponent ngOnInit called');

    // Get payment response data
    this.paymentData = this.paymentResponse.paymentResponseValue?.data;
    this.bookingRequest = this.paymentRepsponseService.bookingRequestData;

    // Get booking ID from route parameter
    this.routeSubscription = this.route.params.subscribe(params => {
      this.bookingId = params['id'];
      this.logger.debug('Booking ID from route:', this.bookingId);

      if (this.bookingId) {
        this.fetchBookingDetails();
      } else {
        this.logger.error('No booking ID found in route');
        this.isLoading = false;
      }
    });
  }

  ngOnDestroy(): void {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.bookingSubscription) {
      this.bookingSubscription.unsubscribe();
    }
  }

  private fetchBookingDetails(): void {
    this.isLoading = true;
    this.bookingSubscription = this.bookingAPIService.getBookingById(this.bookingId).subscribe(
      (response: any) => {
        this.isLoading = false;
        this.logger.debug('Booking details response:', response);

        if (response.succeeded && response.data) {
          this.bookingData = this.mapApiResponseToBookingData(response.data);
        } else {
          this.logger.error('Failed to fetch booking details:', response.message);
          // Fallback to payment response data if available
          this.usePaymentResponseData();
        }
      },
      (error) => {
        this.isLoading = false;
        this.logger.error('Error fetching booking details:', error);
        // Fallback to payment response data if available
        this.usePaymentResponseData();
      }
    );
  }

  private usePaymentResponseData(): void {
    // Use data from payment response and booking request as fallback
    if (this.bookingRequest && this.paymentData) {
      this.bookingData = {
        bookingId: this.bookingId,
        tripType: this.bookingRequest.tripType,
        pickUpCity: this.bookingRequest.pickUpCity,
        dropOffCity: this.bookingRequest.dropOffCity,
        pickUpAddress: this.bookingRequest.pickUpAddress,
        dropOffAddress: this.bookingRequest.dropOffAddress,
        pickUpDate: this.bookingRequest.pickUpDate,
        pickUpTime: this.bookingRequest.pickUpTime,
        carCategory: this.bookingRequest.carCategory,
        carFeatures: this.bookingRequest.carFeatures,
        carCapacity: this.bookingRequest.carCapacity,
        distance: this.bookingRequest.distance,
        duration: this.bookingRequest.duration,
        basicFare: this.bookingRequest.basicFare,
        gst: this.bookingRequest.gst,
        totalFare: this.paymentData.amount || this.bookingRequest.fare,
        paymentType: this.paymentData.paymentType || 'UNKNOWN',
        paymentStatus: this.paymentData.paymentStatus || 'Paid',
        amountPaid: this.paymentData.amount || this.bookingRequest.fare,
        remainingAmountForDriver: this.paymentData.remainingAmountForDriver || 0,
        travelerName: this.bookingRequest.travelerName,
        phoneNumber: this.bookingRequest.phoneNumber,
        mailId: this.bookingRequest.mailId
      };
    }
  }

  // Map API response to our expected format
  private mapApiResponseToBookingData(apiData: any): BookingReceiptData {
    // Calculate correct total fare
    const calculatedTotalFare = apiData.basicFare + apiData.gst;

    // Determine if this is a partial payment based on paymentOption
    const isPartialPayment = apiData.paymentOption === 1;

    // Calculate correct remaining amount for driver
    let remainingAmountForDriver = 0;
    if (isPartialPayment) {
      // For partial payments, always use cashAmountToPayDriver as it's the correct value
      remainingAmountForDriver = apiData.cashAmountToPayDriver || 0;
    } else {
      // For full payments, should be 0
      remainingAmountForDriver = 0;
    }

    // Determine correct payment type
    const paymentType = isPartialPayment ? 'PARTIAL' : 'FULL';

    return {
      bookingId: apiData.bookingID, // Note: API returns bookingID, not bookingId
      tripType: apiData.tripType,
      pickUpCity: apiData.pickUpCity,
      dropOffCity: apiData.dropOffCity,
      pickUpAddress: apiData.pickUpAddress,
      dropOffAddress: apiData.dropOffAddress,
      pickUpDate: apiData.pickUpDate || this.bookingRequest?.pickUpDate || 'Not specified',
      pickUpTime: apiData.pickUpTime || this.bookingRequest?.pickUpTime || 'Not specified',
      carCategory: apiData.carCategory,
      carFeatures: '', // Remove car features as requested
      carCapacity: this.bookingRequest?.carCapacity || 0, // Not in API response
      distance: apiData.distance,
      duration: apiData.duration,
      basicFare: apiData.basicFare,
      gst: apiData.gst,
      totalFare: calculatedTotalFare, // Use calculated total fare (basicFare + gst)
      paymentType: paymentType, // Use corrected payment type
      paymentStatus: apiData.paymentStatus || apiData.razorpayStatus || 'Paid',
      amountPaid: apiData.fare, // Amount paid online
      remainingAmountForDriver: remainingAmountForDriver, // Corrected remaining amount
      travelerName: apiData.travelerName,
      phoneNumber: apiData.phoneNumber,
      mailId: apiData.mailId
    };
  }

  navigateToHome = () => {
    this.router.navigate(['home']);
  }
}
